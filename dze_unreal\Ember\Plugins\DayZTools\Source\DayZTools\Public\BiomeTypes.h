#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "BiomeTypes.generated.h"

UENUM(BlueprintType)
enum class EBiomeType : uint8
{
    None         UMETA(DisplayName = "None"),
    Forest       UMETA(DisplayName = "Forest"),
    Grassland    UMETA(DisplayName = "Grassland"),
    Alpine       UMETA(DisplayName = "Alpine"),
    Wetland      UMETA(DisplayName = "Wetland"),
    Desert       UMETA(DisplayName = "Desert"),
    Tundra       UMETA(DisplayName = "Tundra"),
    Beach        UMETA(DisplayName = "Beach"),
    Urban        UMETA(DisplayName = "Urban")
};

UENUM(BlueprintType)
enum class ETerrainAttribute : uint8
{
    Altitude        UMETA(DisplayName = "Altitude"),
    Slope           UMETA(DisplayName = "Slope"),
    Aspect          UMETA(DisplayName = "Aspect"),
    Curvature       UMETA(DisplayName = "Curvature"),
    Occlusion       UMETA(DisplayName = "Occlusion"),
    FlowAccumulation UMETA(DisplayName = "Flow Accumulation"),
    WindExposure    UMETA(DisplayName = "Wind Exposure"),
    SolarExposure   UMETA(DisplayName = "Solar Exposure"),
    Moisture        UMETA(DisplayName = "Moisture"),
    Temperature     UMETA(DisplayName = "Temperature")
};

USTRUCT(BlueprintType)
struct FTerrainAttributeRange
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    ETerrainAttribute Attribute = ETerrainAttribute::Altitude;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MinValue = 0.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MaxValue = 1.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float Weight = 1.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    UCurveFloat* ResponseCurve = nullptr;
};

// Replace FVegetationInstance with these new structures:

USTRUCT(BlueprintType)
struct FVegetationVariation
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    TSoftObjectPtr<UStaticMesh> Mesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Weight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    bool bAlignToTerrain = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    bool bRandomRotation = true;
};

USTRUCT(BlueprintType)
struct FVegetationSize
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size")
    FString SizeName = "Default Size";

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float ScaleMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size")
    TArray<FVegetationVariation> Variations;

    // The viability range will be auto-calculated based on array position
};

USTRUCT(BlueprintType)
struct FViabilityNoiseSettings
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    bool bEnabled = false;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "bEnabled"))
    float Scale = 100.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "bEnabled"))
    FVector2D Offset = FVector2D::ZeroVector;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise", meta = (EditCondition = "bEnabled", ClampMin = "0.0", ClampMax = "1.0"))
    float Amplitude = 0.5f;
};

USTRUCT(BlueprintType)
struct FViabilityAttribute
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    ETerrainAttribute Attribute = ETerrainAttribute::Altitude;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute", meta = (ClampMin = "-3.0", ClampMax = "3.0"))
    float Power = 1.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
    UCurveFloat* Ramp = nullptr;
};

USTRUCT(BlueprintType)
struct FViabilityDataGroup
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Group")
    FString GroupName = "New Group";
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Group")
    TArray<FViabilityAttribute> Attributes;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Group")
    FViabilityNoiseSettings Noise;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Group", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float GroupWeight = 1.0f;
};

// Update FBiomeSpecies - replace FavoredAttributes with ViabilityDataGroups:
USTRUCT(BlueprintType)
struct FBiomeSpecies
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    FString SpeciesName = "Unknown Species";

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TArray<FVegetationSize> Sizes;

    // Replace FavoredAttributes with this:
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TArray<FViabilityDataGroup> ViabilityDataGroups;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (ClampMin = "0", ClampMax = "100"))
    int32 Priority = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (ClampMin = "10.0", ClampMax = "5000.0"))
    float PriorityRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (ClampMin = "10.0", ClampMax = "2000.0"))
    float ViabilityRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float DensityPerSquareMeter = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    UCurveLinearColor* ColorByViabilityCurve = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    bool bModifiesTerrain = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (EditCondition = "bModifiesTerrain"))
    float TerrainDeformationHeight = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (EditCondition = "bModifiesTerrain"))
    float TerrainDeformationRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species", meta = (EditCondition = "bModifiesTerrain"))
    class ULandscapeLayerInfoObject* TerrainLayer = nullptr;
};