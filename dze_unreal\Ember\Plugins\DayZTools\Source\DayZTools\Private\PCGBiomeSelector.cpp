#include "PCGBiomeSelector.h"
#include "PCGContext.h"
#include "Data/PCGPointData.h"
#include "Metadata/PCGMetadata.h"
#include "Metadata/PCGMetadataAccessor.h"
#include "BiomeData.h"

UPCGBiomeSelectorSettings::UPCGBiomeSelectorSettings()
{
    bUseSeed = false;
}

FPCGElementPtr UPCGBiomeSelectorSettings::CreateElement() const
{
    return MakeShared<FPCGBiomeSelectorElement>();
}

TArray<FPCGPinProperties> UPCGBiomeSelectorSettings::InputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    FPCGPinProperties& InputPin = Properties.Emplace_GetRef(PCGPinConstants::DefaultInputLabel, EPCGDataType::Point);
    InputPin.SetRequiredPin();
    return Properties;
}

TArray<FPCGPinProperties> UPCGBiomeSelectorSettings::OutputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    Properties.Emplace(PCGPinConstants::DefaultOutputLabel, EPCGDataType::Point);
    return Properties;
}

bool FPCGBiomeSelectorElement::ExecuteInternal(FPCGContext* Context) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FPCGBiomeSelectorElement::Execute);

    const UPCGBiomeSelectorSettings* Settings = Context->GetInputSettings<UPCGBiomeSelectorSettings>();
    check(Settings);

    // Get input points
    const TArray<FPCGTaggedData> Inputs = Context->InputData.GetInputsByPin(PCGPinConstants::DefaultInputLabel);
    if (Inputs.IsEmpty())
    {
        return true;
    }

    const UPCGPointData* InputPointData = Cast<UPCGPointData>(Inputs[0].Data);
    if (!InputPointData)
    {
        PCGE_LOG(Error, GraphAndLog, FText::FromString("Input is not point data"));
        return true;
    }

    // Create output
    UPCGPointData* OutputData = NewObject<UPCGPointData>();
    OutputData->InitializeFromData(InputPointData);
    TArray<FPCGPoint>& OutputPoints = OutputData->GetMutablePoints();
    OutputPoints = InputPointData->GetPoints();

    // Get metadata
    UPCGMetadata* Metadata = OutputData->MutableMetadata();

    // Create biome attributes
    FPCGMetadataAttribute<FString>* MainBiomeAttribute =
        Metadata->CreateAttribute<FString>(TEXT("MainBiome"), TEXT("None"), false, false);
    FPCGMetadataAttribute<FString>* SubBiomeAttribute =
        Metadata->CreateAttribute<FString>(TEXT("SubBiome"), TEXT("None"), false, false);
    FPCGMetadataAttribute<float>* BiomeViabilityAttribute =
        Metadata->CreateAttribute<float>(TEXT("BiomeViability"), 0.0f, true, false);

    // Create color attribute for debug visualization
    FPCGMetadataAttribute<FVector>* BiomeColorAttribute = nullptr;
    if (Settings->bDebugMode && Settings->bShowBiomeColors)
    {
        BiomeColorAttribute = Metadata->CreateAttribute<FVector>(TEXT("BiomeColor"), FVector::ZeroVector, false, false);
    }

    // Process each point
    for (FPCGPoint& Point : OutputPoints)
    {
        // Select main biome
        UBiomeData* SelectedMainBiome = SelectBiome(Point, Metadata, Settings->MainBiomes);

        if (SelectedMainBiome)
        {
            MainBiomeAttribute->SetValue(Point.MetadataEntry, SelectedMainBiome->BiomeName);

            // Calculate and store viability
            float Viability = CalculateBiomeViability(Point, Metadata, SelectedMainBiome);
            BiomeViabilityAttribute->SetValue(Point.MetadataEntry, Viability);

            // Set debug color if enabled
            if (BiomeColorAttribute)
            {
                FVector ColorVec(
                    SelectedMainBiome->DebugColor.R / 255.0f,
                    SelectedMainBiome->DebugColor.G / 255.0f,
                    SelectedMainBiome->DebugColor.B / 255.0f
                );
                BiomeColorAttribute->SetValue(Point.MetadataEntry, ColorVec);
            }

            // Select sub-biome if enabled
            if (Settings->bUseSubBiomes)
            {
                // Filter sub-biomes that belong to this main biome
                TArray<FBiomeSelectionRule> ValidSubBiomes;
                for (const FBiomeSelectionRule& SubBiomeRule : Settings->SubBiomes)
                {
                    if (SubBiomeRule.BiomeData && SubBiomeRule.BiomeData->bIsSubBiome)
                    {
                        if (SubBiomeRule.BiomeData->ParentBiomes.Contains(SelectedMainBiome->BiomeType))
                        {
                            ValidSubBiomes.Add(SubBiomeRule);
                        }
                    }
                }

                // Select from valid sub-biomes
                UBiomeData* SelectedSubBiome = SelectBiome(Point, Metadata, ValidSubBiomes);
                if (SelectedSubBiome)
                {
                    SubBiomeAttribute->SetValue(Point.MetadataEntry, SelectedSubBiome->BiomeName);
                }
            }
        }
    }

    // Add output
    FPCGTaggedData& Output = Context->OutputData.TaggedData.Emplace_GetRef();
    Output.Data = OutputData;

    return true;
}

float FPCGBiomeSelectorElement::CalculateBiomeViability(
    const FPCGPoint& Point,
    const UPCGMetadata* Metadata,
    const UBiomeData* BiomeData) const
{
    if (!BiomeData || !Metadata)
    {
        return 0.0f;
    }

    float TotalViability = 0.0f;
    float TotalWeight = 0.0f;

    // Evaluate each biome condition
    for (const FTerrainAttributeRange& Condition : BiomeData->BiomeConditions)
    {
        float AttributeValue = 0.0f;

        // Get attribute value based on type
        FString AttributeName;
        switch (Condition.Attribute)
        {
        case ETerrainAttribute::Altitude: AttributeName = TEXT("Altitude"); break;
        case ETerrainAttribute::Slope: AttributeName = TEXT("Slope"); break;
        case ETerrainAttribute::Aspect: AttributeName = TEXT("Aspect"); break;
        case ETerrainAttribute::Curvature: AttributeName = TEXT("Curvature"); break;
        case ETerrainAttribute::Occlusion: AttributeName = TEXT("Occlusion"); break;
        case ETerrainAttribute::FlowAccumulation: AttributeName = TEXT("FlowAccumulation"); break;
        case ETerrainAttribute::WindExposure: AttributeName = TEXT("WindExposure"); break;
        case ETerrainAttribute::SolarExposure: AttributeName = TEXT("SolarExposure"); break;
        case ETerrainAttribute::Moisture: AttributeName = TEXT("Moisture"); break;
        case ETerrainAttribute::Temperature: AttributeName = TEXT("Temperature"); break;
        }

        // Try to get the attribute
        const FPCGMetadataAttributeBase* AttributeBase =
            Metadata->GetConstAttribute(FName(*AttributeName));
        if (AttributeBase && AttributeBase->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
        {
            AttributeValue =
                static_cast<const FPCGMetadataAttribute<float>*>(AttributeBase)
                ->GetValueFromItemKey(Point.MetadataEntry);
        }

        // Calculate viability for this condition
        float ConditionViability = 0.0f;

        if (Condition.ResponseCurve)
        {
            // Use response curve if available
            ConditionViability = Condition.ResponseCurve->GetFloatValue(AttributeValue);
        }
        else
        {
            // Linear interpolation within range
            if (AttributeValue >= Condition.MinValue && AttributeValue <= Condition.MaxValue)
            {
                ConditionViability = 1.0f;
            }
            else
            {
                // Fade out beyond range
                if (AttributeValue < Condition.MinValue)
                {
                    float Distance = Condition.MinValue - AttributeValue;
                    ConditionViability = FMath::Max(0.0f, 1.0f - Distance * 10.0f);
                }
                else
                {
                    float Distance = AttributeValue - Condition.MaxValue;
                    ConditionViability = FMath::Max(0.0f, 1.0f - Distance * 10.0f);
                }
            }
        }

        TotalViability += ConditionViability * Condition.Weight;
        TotalWeight += Condition.Weight;
    }

    // Normalize by total weight
    if (TotalWeight > 0.0f)
    {
        return TotalViability / TotalWeight;
    }

    return 0.0f;
}

UBiomeData* FPCGBiomeSelectorElement::SelectBiome(const FPCGPoint& Point, const UPCGMetadata* Metadata, const TArray<FBiomeSelectionRule>& BiomeRules) const
{
    UBiomeData* BestBiome = nullptr;
    float BestScore = -1.0f;

    for (const FBiomeSelectionRule& Rule : BiomeRules)
    {
        if (!Rule.bEnabled || !Rule.BiomeData)
        {
            continue;
        }

        float Viability = CalculateBiomeViability(Point, Metadata, Rule.BiomeData);
        float Score = Viability * Rule.Priority;

        if (Score > BestScore)
        {
            BestScore = Score;
            BestBiome = Rule.BiomeData;
        }
    }

    return BestBiome;
}