#include "BiomeDataEditor.h"
#include "EditorStyleSet.h"
#include "Widgets/Docking/SDockTab.h"
#include "PropertyEditorModule.h"
#include "IDetailsView.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SComboBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SSplitter.h"
#include "Toolkits/ToolkitManager.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Engine/Texture2D.h"
#include "Engine/Texture2DDynamic.h"
#include "LandscapeStreamingProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeDataAccess.h"
#include "EngineUtils.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Misc/MessageDialog.h"
#include "Slate/SlateTextures.h"
#include "PropertyCustomizationHelpers.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "Factories/TextureFactory.h"
#include "Misc/PackageName.h"
#include "UObject/SavePackage.h"
#include "AssetRegistry/AssetRegistryModule.h"

const FName FBiomeDataEditor::BiomeDataEditorAppIdentifier = FName("BiomeDataEditor");
const FName FBiomeDataEditor::DetailsTabId = FName("BiomeDataEditor_Details");
const FName FBiomeDataEditor::ViabilityPreviewTabId = FName("BiomeDataEditor_ViabilityPreview");

FBiomeDataEditor::FBiomeDataEditor()
{
}

FBiomeDataEditor::~FBiomeDataEditor()
{
}

void FBiomeDataEditor::InitBiomeDataEditor(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UBiomeData* InBiomeData)
{
    BiomeDataAsset = InBiomeData;

    // Create details view with custom details
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bAllowSearch = true;
    DetailsViewArgs.bShowOptions = true;
    DetailsViewArgs.bShowModifiedPropertiesOption = true;

    DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);
    DetailsView->SetObject(BiomeDataAsset);

    // Register custom details
    DetailsView->RegisterInstancedCustomPropertyLayout(
        UBiomeData::StaticClass(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FBiomeDataDetailsCustomization::MakeInstance)
    );

    // Create preview widget
    ViabilityPreviewWidget = SNew(SBiomeViabilityPreviewWidget, this);

    // Initialize preview textures
    CreatePreviewTextures();

    // Layout
    const TSharedRef<FTabManager::FLayout> StandaloneDefaultLayout = FTabManager::NewLayout("BiomeDataEditor_Layout_v1")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()
            ->SetOrientation(Orient_Horizontal)
            ->Split
            (
                FTabManager::NewStack()
                ->SetSizeCoefficient(0.6f)
                ->AddTab(DetailsTabId, ETabState::OpenedTab)
            )
            ->Split
            (
                FTabManager::NewStack()
                ->SetSizeCoefficient(0.4f)
                ->AddTab(ViabilityPreviewTabId, ETabState::OpenedTab)
            )
        );

    InitAssetEditor(Mode, InitToolkitHost, BiomeDataEditorAppIdentifier, StandaloneDefaultLayout, true, true, InBiomeData);
}

TSharedRef<SDockTab> FBiomeDataEditor::SpawnDetailsTab(const FSpawnTabArgs& Args)
{
    return SNew(SDockTab)
        .Label(FText::FromString("Details"))
        [
            DetailsView.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FBiomeDataEditor::SpawnViabilityPreviewTab(const FSpawnTabArgs& Args)
{
    return SNew(SDockTab)
        .Label(FText::FromString("Viability Preview"))
        [
            ViabilityPreviewWidget.ToSharedRef()
        ];
}

void FBiomeDataEditor::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    InTabManager->RegisterTabSpawner(DetailsTabId, FOnSpawnTab::CreateSP(this, &FBiomeDataEditor::SpawnDetailsTab))
        .SetDisplayName(FText::FromString("Details"));

    InTabManager->RegisterTabSpawner(ViabilityPreviewTabId, FOnSpawnTab::CreateSP(this, &FBiomeDataEditor::SpawnViabilityPreviewTab))
        .SetDisplayName(FText::FromString("Viability Preview"));
}

void FBiomeDataEditor::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    InTabManager->UnregisterTabSpawner(DetailsTabId);
    InTabManager->UnregisterTabSpawner(ViabilityPreviewTabId);
}

void FBiomeDataEditor::CreatePreviewTextures()
{
    UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Starting texture creation..."));

    int32 TextureSize = 512;
    FString BasePath = TEXT("/Game/PCG/ViabilityPreview/");

    UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Texture size: %d, Base path: %s"), TextureSize, *BasePath);

    // Helper lambda to create or load texture asset
    auto CreateOrLoadTexture = [TextureSize, BasePath](const FString& Name) -> UTexture2D*
        {
            FString AssetPath = BasePath + Name;
            FString PackagePath = AssetPath;

            // Try to load existing texture first
            UTexture2D* ExistingTexture = LoadObject<UTexture2D>(nullptr, *AssetPath);
            if (ExistingTexture)
            {
                UE_LOG(LogTemp, Warning, TEXT("Loaded existing texture: %s"), *AssetPath);
                return ExistingTexture;
            }

            // Create new texture asset using direct approach
            UPackage* Package = CreatePackage(*PackagePath);
            if (!Package)
            {
                UE_LOG(LogTemp, Error, TEXT("Failed to create package: %s"), *PackagePath);
                return nullptr;
            }

            // Create the texture object directly
            UTexture2D* NewTexture = NewObject<UTexture2D>(Package, *Name, RF_Public | RF_Standalone);
            if (!NewTexture)
            {
                UE_LOG(LogTemp, Error, TEXT("Failed to create texture object: %s"), *Name);
                return nullptr;
            }

            // Initialize texture properties
            NewTexture->Source.Init(TextureSize, TextureSize, 1, 1, TSF_BGRA8);
            NewTexture->CompressionSettings = TC_Default;
            NewTexture->SRGB = true;
            NewTexture->Filter = TF_Bilinear;
            NewTexture->AddressX = TA_Clamp;
            NewTexture->AddressY = TA_Clamp;

            // Initialize with black pixels
            TArray<FColor> InitialData;
            InitialData.SetNum(TextureSize * TextureSize);
            for (int32 i = 0; i < InitialData.Num(); i++)
            {
                InitialData[i] = FColor::Black;
            }

            // Copy data to texture source
            uint8* MipData = NewTexture->Source.LockMip(0);
            FMemory::Memcpy(MipData, InitialData.GetData(), InitialData.Num() * sizeof(FColor));
            NewTexture->Source.UnlockMip(0);

            // Update and save
            NewTexture->UpdateResource();
            NewTexture->MarkPackageDirty();
            Package->MarkPackageDirty();

            // Register with asset registry
            FAssetRegistryModule::AssetCreated(NewTexture);

            UE_LOG(LogTemp, Warning, TEXT("Created new texture asset: %s"), *AssetPath);
            return NewTexture;
        };

    // Create main viability preview
    ViabilityPreviewTexture = CreateOrLoadTexture("ViabilityPreview");
    UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Main viability texture: %s"),
        ViabilityPreviewTexture ? *ViabilityPreviewTexture->GetName() : TEXT("NULL"));

    // Create terrain attribute textures
    TArray<FString> AttributeNames = {
        TEXT("Height"), TEXT("Slope"), TEXT("Aspect"), TEXT("Curvature"),
        TEXT("Occlusion"), TEXT("FlowAccumulation"), TEXT("WindExposure"), TEXT("Normal")
    };

    UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Creating %d attribute textures..."), AttributeNames.Num());

    for (const FString& AttrName : AttributeNames)
    {
        FString TextureName = FString::Printf(TEXT("ViabilityPreview_%s"), *AttrName);
        UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Creating texture: %s"), *TextureName);

        UTexture2D* AttrTexture = CreateOrLoadTexture(TextureName);
        if (AttrTexture)
        {
            TerrainAttributeTextures.Add(AttrName, AttrTexture);
            UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Successfully created/loaded texture: %s"), *AttrName);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("CreatePreviewTextures: Failed to create texture: %s"), *AttrName);
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("CreatePreviewTextures: Finished. Total textures created: %d"), TerrainAttributeTextures.Num());
}

void FBiomeDataEditor::BakeTerrainData()
{
    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Starting terrain data baking..."));

    if (!SelectedLandscapeProxy.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("BakeTerrainData: No landscape proxy selected"));
        FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("Please select a Landscape Streaming Proxy first!"));
        return;
    }

    ALandscapeStreamingProxy* Landscape = SelectedLandscapeProxy.Get();
    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Using landscape: %s"), *Landscape->GetName());

    // Get landscape bounds
    Landscape->GetActorBounds(false, CachedLandscapeOrigin, CachedLandscapeExtent);
    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Landscape bounds - Origin: %s, Extent: %s"),
        *CachedLandscapeOrigin.ToString(), *CachedLandscapeExtent.ToString());

    int32 TextureSize = 512;
    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Texture size: %d"), TextureSize);

    // First pass: detect actual height range
    float MinHeight = FLT_MAX;
    float MaxHeight = -FLT_MAX;

    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Detecting height range..."));

    // Sample a grid to find min/max heights
    int32 SampleCount = 64; // Lower resolution for height detection
    for (int32 Y = 0; Y < SampleCount; Y++)
    {
        for (int32 X = 0; X < SampleCount; X++)
        {
            float U = (float)X / (SampleCount - 1);
            float V = (float)Y / (SampleCount - 1);

            FVector WorldPos = CachedLandscapeOrigin + FVector(
                FMath::Lerp(-CachedLandscapeExtent.X, CachedLandscapeExtent.X, U),
                FMath::Lerp(-CachedLandscapeExtent.Y, CachedLandscapeExtent.Y, V),
                0
            );

            float Height = GetWorldHeightAtLocation(WorldPos, Landscape);
            MinHeight = FMath::Min(MinHeight, Height);
            MaxHeight = FMath::Max(MaxHeight, Height);
        }
    }

    // Store the detected range
    CachedMinHeight = MinHeight;
    CachedMaxHeight = MaxHeight;

    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Detected height range: Min=%.2f, Max=%.2f"), MinHeight, MaxHeight);

    // Check if we have textures
    UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Available textures: %d"), TerrainAttributeTextures.Num());
    for (const auto& Pair : TerrainAttributeTextures)
    {
        UE_LOG(LogTemp, Warning, TEXT("BakeTerrainData: Texture '%s' = %s"),
            *Pair.Key, Pair.Value ? *Pair.Value->GetName() : TEXT("NULL"));
    }

    // Show progress notification
    FNotificationInfo Info(FText::FromString("Baking terrain data..."));
    Info.bFireAndForget = false;
    Info.ExpireDuration = 2.0f;

    auto NotificationItem = FSlateNotificationManager::Get().AddNotification(Info);
    NotificationItem->SetCompletionState(SNotificationItem::CS_Pending);

    // Calculate terrain attributes for each texture
    for (auto& Pair : TerrainAttributeTextures)
    {
        const FString& AttributeName = Pair.Key;
        UTexture2D* Texture = Pair.Value;

        if (!Texture)
        {
            UE_LOG(LogTemp, Error, TEXT("Texture is null for attribute: %s"), *AttributeName);
            continue;
        }

        // Create texture data array
        TArray<FColor> TextureData;
        TextureData.SetNum(TextureSize * TextureSize);

        // Sample landscape and calculate attribute
        for (int32 Y = 0; Y < TextureSize; Y++)
        {
            for (int32 X = 0; X < TextureSize; X++)
            {
                // Convert texture coords to world position
                float U = (float)X / (TextureSize - 1);
                float V = (float)Y / (TextureSize - 1);

                FVector WorldPos = CachedLandscapeOrigin + FVector(
                    FMath::Lerp(-CachedLandscapeExtent.X, CachedLandscapeExtent.X, U),
                    FMath::Lerp(-CachedLandscapeExtent.Y, CachedLandscapeExtent.Y, V),
                    0
                );

                // Calculate attribute value
                if (AttributeName == TEXT("Normal"))
                {
                    // Special handling for normal map - store as RGB
                    FVector Normal = GetNormalAtLocation(WorldPos, Landscape);

                    // Ensure normal is normalized and valid
                    if (!Normal.IsNearlyZero())
                    {
                        Normal.Normalize();
                    }
                    else
                    {
                        Normal = FVector::UpVector; // Default to up if invalid
                    }

                    // Convert normal to standard normal map encoding
                    // X and Y: [-1,1] -> [0,255]
                    // Z: [0,1] -> [128,255] (since surface normals typically point up)
                    uint8 R = FMath::Clamp(FMath::RoundToInt((Normal.X + 1.0f) * 127.5f), 0, 255);
                    uint8 G = FMath::Clamp(FMath::RoundToInt((Normal.Y + 1.0f) * 127.5f), 0, 255);
                    uint8 B = FMath::Clamp(FMath::RoundToInt(Normal.Z * 127.5f + 127.5f), 128, 255);

                    int32 Index = Y * TextureSize + X;
                    TextureData[Index] = FColor(R, G, B, 255);

                    // Debug logging for first few normal samples
                    static int32 NormalDebugCount = 0;
                    if (NormalDebugCount < 5)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("Normal Debug: Normal=(%f,%f,%f), RGB=(%d,%d,%d)"),
                            Normal.X, Normal.Y, Normal.Z, R, G, B);
                        NormalDebugCount++;
                    }
                }
                else
                {
                    float Value = 0.0f;
                    if (AttributeName == TEXT("Height"))
                    {
                        Value = CalculateHeightAtLocation(WorldPos, Landscape);
                    }
                    else if (AttributeName == TEXT("Slope"))
                    {
                        Value = CalculateSlopeAtLocation(WorldPos, Landscape);
                    }
                    else if (AttributeName == TEXT("Aspect"))
                    {
                        Value = CalculateAspectAtLocation(WorldPos, Landscape);
                    }
                    else if (AttributeName == TEXT("Curvature"))
                    {
                        Value = CalculateCurvatureAtLocation(WorldPos, Landscape);
                    }
                    else if (AttributeName == TEXT("Occlusion"))
                    {
                        Value = CalculateOcclusionAtLocation(WorldPos, Landscape);
                    }
                    else if (AttributeName == TEXT("FlowAccumulation"))
                    {
                        Value = CalculateFlowAccumulationAtLocation(WorldPos, Landscape);
                    }
                    else if (AttributeName == TEXT("WindExposure"))
                    {
                        Value = CalculateWindExposureAtLocation(WorldPos, Landscape, FVector(1, 0, 0));
                    }

                    // Write to texture data array
                    uint8 ByteValue = FMath::Clamp(FMath::RoundToInt(Value * 255.0f), 0, 255);
                    int32 Index = Y * TextureSize + X;
                    TextureData[Index] = FColor(ByteValue, ByteValue, ByteValue, 255);
                }
            }
        }

        // Update texture source data
        uint8* MipData = Texture->Source.LockMip(0);
        FMemory::Memcpy(MipData, TextureData.GetData(), TextureData.Num() * sizeof(FColor));
        Texture->Source.UnlockMip(0);

        // Update and mark dirty
        Texture->UpdateResource();
        Texture->MarkPackageDirty();

        UE_LOG(LogTemp, Warning, TEXT("Updated texture data for attribute: %s"), *AttributeName);
    }

    NotificationItem->SetText(FText::FromString("Terrain data baked successfully!"));
    NotificationItem->SetCompletionState(SNotificationItem::CS_Success);
    NotificationItem->ExpireAndFadeout();
}

void FBiomeDataEditor::PreviewSpeciesViability(int32 SpeciesIndex)
{
    if (!BiomeDataAsset || SpeciesIndex >= BiomeDataAsset->Species.Num())
    {
        return;
    }

    if (TerrainAttributeTextures.Num() == 0)
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("Please bake terrain data first!"));
        return;
    }

    const FBiomeSpecies& Species = BiomeDataAsset->Species[SpeciesIndex];
    int32 TextureSize = 512;

    // Get mip 0
    FTexture2DMipMap* Mip0 = &ViabilityPreviewTexture->GetPlatformData()->Mips[0];
    FColor* ViabilityData = (FColor*)Mip0->BulkData.Lock(LOCK_READ_WRITE);

    // Read all terrain attribute data
    TMap<FString, TArray<float>> AttributeData;
    for (const auto& Pair : TerrainAttributeTextures)
    {
        const FString& AttrName = Pair.Key;
        UTexture2D* AttrTexture = Pair.Value;

        TArray<float>& Data = AttributeData.Add(AttrName);
        Data.SetNum(TextureSize * TextureSize);

        FTexture2DMipMap* AttrMip = &AttrTexture->GetPlatformData()->Mips[0];
        FColor* AttrData = (FColor*)AttrMip->BulkData.Lock(LOCK_READ_ONLY);

        for (int32 i = 0; i < TextureSize * TextureSize; i++)
        {
            Data[i] = AttrData[i].R / 255.0f;
        }

        AttrMip->BulkData.Unlock();
    }

    // Calculate viability for each pixel
    for (int32 Y = 0; Y < TextureSize; Y++)
    {
        for (int32 X = 0; X < TextureSize; X++)
        {
            int32 Index = Y * TextureSize + X;

            // Build attributes map for this pixel
            TMap<FString, float> PixelAttributes;
            for (const auto& Pair : AttributeData)
            {
                PixelAttributes.Add(Pair.Key, Pair.Value[Index]);
            }

            // Calculate world position for noise
            float U = (float)X / (TextureSize - 1);
            float V = (float)Y / (TextureSize - 1);
            FVector2D WorldPos2D(
                FMath::Lerp(-CachedLandscapeExtent.X, CachedLandscapeExtent.X, U),
                FMath::Lerp(-CachedLandscapeExtent.Y, CachedLandscapeExtent.Y, V)
            );

            // Calculate viability
            float Viability = CalculateViabilityFromAttributes(Species, PixelAttributes, WorldPos2D);

            // Convert to color
            FLinearColor Color = GetViabilityColor(Viability);
            ViabilityData[Index] = Color.ToFColor(true);
        }
    }

    // Unlock and update
    Mip0->BulkData.Unlock();
    ViabilityPreviewTexture->UpdateResource();

    // Update preview widget
    ViabilityPreviewWidget->SetPreviewTexture(ViabilityPreviewTexture);
}

float FBiomeDataEditor::CalculateHeightAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    // Get actual world height first
    float WorldHeight = GetWorldHeightAtLocation(Location, Landscape);

    // Normalize height to 0-1 range using detected min/max
    if (FMath::Abs(CachedMaxHeight - CachedMinHeight) < SMALL_NUMBER)
    {
        return 0.5f; // Flat landscape
    }

    float NormalizedHeight = FMath::Clamp((WorldHeight - CachedMinHeight) / (CachedMaxHeight - CachedMinHeight), 0.0f, 1.0f);

    // Debug logging for first few samples
    static int32 HeightDebugCount = 0;
    if (HeightDebugCount < 5)
    {
        UE_LOG(LogTemp, Warning, TEXT("Height Debug: WorldHeight=%f, Min=%f, Max=%f, Normalized=%f"),
            WorldHeight, CachedMinHeight, CachedMaxHeight, NormalizedHeight);
        HeightDebugCount++;
    }

    return NormalizedHeight;
}

float FBiomeDataEditor::GetWorldHeightAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return Location.Z;

    FVector Start = Location + FVector(0, 0, 10000.0f);
    FVector End = Location - FVector(0, 0, 10000.0f);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnFaceIndex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    if (Landscape->GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, QueryParams))
    {
        if (Cast<ALandscapeProxy>(HitResult.GetActor()))
        {
            return HitResult.Location.Z;
        }
    }

    return Location.Z;
}

FVector FBiomeDataEditor::GetNormalAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return FVector::UpVector;

    // Use line trace to get the normal directly
    FVector Start = Location + FVector(0, 0, 1000.0f);
    FVector End = Location - FVector(0, 0, 1000.0f);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnFaceIndex = true;

    if (Landscape->GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, QueryParams))
    {
        if (Cast<ALandscapeProxy>(HitResult.GetActor()))
        {
            return HitResult.Normal;
        }
    }

    // Fallback to calculating normal from height samples
    const float SampleOffset = 100.0f;

    float HeightCenter = GetWorldHeightAtLocation(Location, Landscape);
    float HeightRight = GetWorldHeightAtLocation(Location + FVector(SampleOffset, 0, 0), Landscape);
    float HeightForward = GetWorldHeightAtLocation(Location + FVector(0, SampleOffset, 0), Landscape);
    float HeightLeft = GetWorldHeightAtLocation(Location + FVector(-SampleOffset, 0, 0), Landscape);
    float HeightBack = GetWorldHeightAtLocation(Location + FVector(0, -SampleOffset, 0), Landscape);

    // Calculate gradients using central differences for better accuracy
    float Dx = (HeightRight - HeightLeft) / (2.0f * SampleOffset);
    float Dy = (HeightForward - HeightBack) / (2.0f * SampleOffset);

    // Create normal from gradients
    FVector Normal(-Dx, -Dy, 1.0f);
    Normal.Normalize();

    // Debug logging for first few normal calculations
    static int32 NormalCalcDebugCount = 0;
    if (NormalCalcDebugCount < 5)
    {
        UE_LOG(LogTemp, Warning, TEXT("Normal Calc Debug: Heights C=%f R=%f F=%f L=%f B=%f, Gradients Dx=%f Dy=%f, Normal=(%f,%f,%f)"),
            HeightCenter, HeightRight, HeightForward, HeightLeft, HeightBack, Dx, Dy, Normal.X, Normal.Y, Normal.Z);
        NormalCalcDebugCount++;
    }

    return Normal;
}

float FBiomeDataEditor::CalculateSlopeAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    FVector Normal = GetNormalAtLocation(Location, Landscape);

    // Ensure normal is valid
    if (Normal.IsNearlyZero())
    {
        return 0.0f;
    }

    Normal.Normalize();

    // Calculate angle between normal and up vector
    float CosAngle = FVector::DotProduct(Normal, FVector::UpVector);
    CosAngle = FMath::Clamp(CosAngle, -1.0f, 1.0f);

    float SlopeDegrees = FMath::RadiansToDegrees(FMath::Acos(CosAngle));
    float SlopeNormalized = FMath::Clamp(SlopeDegrees / 90.0f, 0.0f, 1.0f);

    // Debug logging for first few samples
    static int32 DebugCount = 0;
    if (DebugCount < 5)
    {
        UE_LOG(LogTemp, Warning, TEXT("Slope Debug: Normal=(%f,%f,%f), CosAngle=%f, SlopeDegrees=%f, Normalized=%f"),
            Normal.X, Normal.Y, Normal.Z, CosAngle, SlopeDegrees, SlopeNormalized);
        DebugCount++;
    }

    return SlopeNormalized;
}

float FBiomeDataEditor::CalculateAspectAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    FVector Normal = GetNormalAtLocation(Location, Landscape);

    // Project normal to horizontal plane
    FVector2D HorizontalNormal(Normal.X, Normal.Y);
    if (HorizontalNormal.IsNearlyZero())
    {
        return 0.0f; // Flat surface, no aspect
    }

    HorizontalNormal.Normalize();

    // Calculate angle from north (Y axis)
    float Angle = FMath::Atan2(HorizontalNormal.X, HorizontalNormal.Y);
    if (Angle < 0) Angle += 2.0f * PI;

    return Angle / (2.0f * PI); // Normalize to 0-1
}

float FBiomeDataEditor::CalculateCurvatureAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.5f;

    const float SampleDistance = 50.0f;

    // Get normals at cardinal points to calculate curvature from normal variation
    FVector NormalCenter = GetNormalAtLocation(Location, Landscape);
    FVector NormalNorth = GetNormalAtLocation(Location + FVector(0, SampleDistance, 0), Landscape);
    FVector NormalSouth = GetNormalAtLocation(Location + FVector(0, -SampleDistance, 0), Landscape);
    FVector NormalEast = GetNormalAtLocation(Location + FVector(SampleDistance, 0, 0), Landscape);
    FVector NormalWest = GetNormalAtLocation(Location + FVector(-SampleDistance, 0, 0), Landscape);

    // Calculate how much the normals change (curvature is the rate of change of normals)
    float CurvatureX = FVector::Dist(NormalEast, NormalCenter) + FVector::Dist(NormalWest, NormalCenter);
    float CurvatureY = FVector::Dist(NormalNorth, NormalCenter) + FVector::Dist(NormalSouth, NormalCenter);

    // Average the curvature in both directions
    float TotalCurvature = (CurvatureX + CurvatureY) * 0.5f;

    // Normalize to 0-1 range (adjust multiplier based on typical curvature values)
    float NormalizedCurvature = FMath::Clamp(TotalCurvature * 2.0f, 0.0f, 1.0f);

    // Debug logging for first few samples
    static int32 CurvatureDebugCount = 0;
    if (CurvatureDebugCount < 5)
    {
        UE_LOG(LogTemp, Warning, TEXT("Curvature Debug: CurvX=%f, CurvY=%f, Total=%f, Normalized=%f"),
            CurvatureX, CurvatureY, TotalCurvature, NormalizedCurvature);
        CurvatureDebugCount++;
    }

    return NormalizedCurvature;
}

float FBiomeDataEditor::CalculateOcclusionAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 1.0f; // Default to no occlusion

    const float Radius = 300.0f;  // Smaller radius for more local AO
    const int32 Samples = 16;     // Directional samples
    const int32 DistanceSamples = 8; // Distance samples per direction

    float TotalOcclusion = 0.0f;
    const float AngleStep = 2.0f * PI / Samples;

    float BaseHeight = GetWorldHeightAtLocation(Location, Landscape);

    for (int32 i = 0; i < Samples; ++i)
    {
        float Angle = i * AngleStep;
        FVector2D Direction(FMath::Cos(Angle), FMath::Sin(Angle));

        float MaxOcclusionInDirection = 0.0f;

        // Sample at multiple distances in this direction
        for (int32 d = 1; d <= DistanceSamples; ++d)
        {
            float Distance = (Radius * d) / DistanceSamples;
            FVector SampleLocation = Location + FVector(Direction.X * Distance, Direction.Y * Distance, 0.0f);
            float SampleHeight = GetWorldHeightAtLocation(SampleLocation, Landscape);

            // Calculate how much this sample occludes the current point
            float HeightDifference = SampleHeight - BaseHeight;

            if (HeightDifference > 0.0f) // Only consider points that are higher
            {
                // Calculate occlusion based on height difference and distance
                // Closer and higher points contribute more to occlusion
                float OcclusionContribution = HeightDifference / (Distance + HeightDifference);
                OcclusionContribution = FMath::Clamp(OcclusionContribution, 0.0f, 1.0f);

                // Use the maximum occlusion found in this direction
                MaxOcclusionInDirection = FMath::Max(MaxOcclusionInDirection, OcclusionContribution);
            }
        }

        TotalOcclusion += MaxOcclusionInDirection;
    }

    // Average the occlusion from all directions
    float AverageOcclusion = TotalOcclusion / Samples;

    // Apply a curve to make the AO more visually appealing
    float FinalOcclusion = FMath::Pow(AverageOcclusion, 0.7f); // Gamma correction for better contrast

    // Return 1 - occlusion so that 1 = fully lit, 0 = fully occluded
    float Result = 1.0f - FMath::Clamp(FinalOcclusion, 0.0f, 1.0f);

    // Debug logging for first few samples
    static int32 OcclusionDebugCount = 0;
    if (OcclusionDebugCount < 5)
    {
        UE_LOG(LogTemp, Warning, TEXT("Occlusion Debug: BaseHeight=%f, TotalOcclusion=%f, Average=%f, Final=%f, Result=%f"),
            BaseHeight, TotalOcclusion, AverageOcclusion, FinalOcclusion, Result);
        OcclusionDebugCount++;
    }

    return Result;
}

float FBiomeDataEditor::CalculateFlowAccumulationAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const
{
    if (!Landscape) return 0.0f;

    const float CellSize = 100.0f;
    const int32 Iterations = 3;
    const float Threshold = 0.01f;
    float AccumulatedFlow = 0.0f;

    const FVector2D Directions[8] = {
        FVector2D(0, 1), FVector2D(1, 1), FVector2D(1, 0), FVector2D(1, -1),
        FVector2D(0, -1), FVector2D(-1, -1), FVector2D(-1, 0), FVector2D(-1, 1)
    };

    const float DiagonalWeight = 1.0f / FMath::Sqrt(2.0f);
    const float CardinalWeight = 1.0f;

    float CurrentHeight = GetWorldHeightAtLocation(Location, Landscape);

    for (int32 iter = 0; iter < Iterations; ++iter)
    {
        float IterationRadius = CellSize * FMath::Pow(2.0f, iter);

        for (int32 dir = 0; dir < 8; ++dir)
        {
            FVector SampleLocation = Location + FVector(Directions[dir].X, Directions[dir].Y, 0) * IterationRadius;
            float SampleHeight = GetWorldHeightAtLocation(SampleLocation, Landscape);

            if (SampleHeight > CurrentHeight + Threshold)
            {
                float Weight = (dir % 2 == 0) ? CardinalWeight : DiagonalWeight;
                AccumulatedFlow += Weight * (SampleHeight - CurrentHeight) / IterationRadius;
            }
        }
    }

    float Result = FMath::Clamp(AccumulatedFlow / Iterations, 0.0f, 1.0f);

    // Debug logging for first few samples
    static int32 FlowDebugCount = 0;
    if (FlowDebugCount < 5)
    {
        UE_LOG(LogTemp, Warning, TEXT("Flow Debug: AccumulatedFlow=%f, Result=%f"), AccumulatedFlow, Result);
        FlowDebugCount++;
    }

    return Result;
}

float FBiomeDataEditor::CalculateWindExposureAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape, const FVector& WindDir) const
{
    if (!Landscape) return 1.0f;

    FVector WindDirection = WindDir.GetSafeNormal();
    const float Radius = 1000.0f;
    const int32 Samples = 10;
    float TotalExposure = 0.0f;

    FVector BaseLocation = Location;
    BaseLocation.Z = GetWorldHeightAtLocation(Location, Landscape);

    for (int32 i = 1; i <= Samples; ++i)
    {
        float Distance = (Radius * i) / Samples;
        FVector SampleLocation = BaseLocation - WindDirection * Distance;
        float SampleHeight = GetWorldHeightAtLocation(SampleLocation, Landscape);

        // Check if wind is blocked
        float HeightDifference = BaseLocation.Z - SampleHeight;
        float Angle = FMath::Atan2(HeightDifference, Distance);

        // More negative angle = more exposed
        float Exposure = FMath::Clamp(1.0f + (Angle / (PI * 0.25f)), 0.0f, 1.0f);
        TotalExposure += Exposure;
    }

    return TotalExposure / Samples;
}

float FBiomeDataEditor::CalculateViabilityFromAttributes(const FBiomeSpecies& Species, const TMap<FString, float>& Attributes, const FVector2D& WorldPos) const
{
    if (Species.ViabilityDataGroups.Num() == 0)
    {
        return 0.0f;
    }

    float TotalViability = 0.0f;

    // Process each data group
    for (const FViabilityDataGroup& DataGroup : Species.ViabilityDataGroups)
    {
        if (DataGroup.Attributes.Num() == 0)
        {
            continue;
        }

        float GroupViability = 1.0f; // Start at 1 for multiplication

        // Process each attribute in the group (multiply together)
        for (const FViabilityAttribute& ViabilityAttr : DataGroup.Attributes)
        {
            float AttributeValue = 0.0f;

            // Get attribute value
            FString AttributeName;
            switch (ViabilityAttr.Attribute)
            {
            case ETerrainAttribute::Altitude: AttributeName = TEXT("Height"); break;
            case ETerrainAttribute::Slope: AttributeName = TEXT("Slope"); break;
            case ETerrainAttribute::Aspect: AttributeName = TEXT("Aspect"); break;
            case ETerrainAttribute::Curvature: AttributeName = TEXT("Curvature"); break;
            case ETerrainAttribute::Occlusion: AttributeName = TEXT("Occlusion"); break;
            case ETerrainAttribute::FlowAccumulation: AttributeName = TEXT("FlowAccumulation"); break;
            case ETerrainAttribute::WindExposure: AttributeName = TEXT("WindExposure"); break;
            default: continue;
            }

            if (const float* ValuePtr = Attributes.Find(AttributeName))
            {
                AttributeValue = *ValuePtr;
            }

            // Apply power
            float ProcessedValue = AttributeValue;
            if (FMath::Abs(ViabilityAttr.Power) > SMALL_NUMBER)
            {
                if (ViabilityAttr.Power < 0)
                {
                    // Negative power inverts the value first
                    ProcessedValue = 1.0f - ProcessedValue;
                    ProcessedValue = FMath::Pow(ProcessedValue, FMath::Abs(ViabilityAttr.Power));
                }
                else
                {
                    ProcessedValue = FMath::Pow(ProcessedValue, ViabilityAttr.Power);
                }
            }

            // Apply ramp/curve if available
            if (ViabilityAttr.Ramp)
            {
                ProcessedValue = ViabilityAttr.Ramp->GetFloatValue(ProcessedValue);
            }

            // Multiply into group viability
            GroupViability *= ProcessedValue;
        }

        // Apply noise if enabled
        if (DataGroup.Noise.bEnabled)
        {
            FVector2D NoisePos = (WorldPos + DataGroup.Noise.Offset) / DataGroup.Noise.Scale;
            float NoiseValue = FMath::PerlinNoise2D(NoisePos);
            NoiseValue = (NoiseValue + 1.0f) * 0.5f; // Remap from [-1,1] to [0,1]
            NoiseValue = FMath::Lerp(1.0f - DataGroup.Noise.Amplitude, 1.0f, NoiseValue);
            GroupViability *= NoiseValue;
        }

        // Apply group weight and add to total
        TotalViability += GroupViability * DataGroup.GroupWeight;
    }

    // Clamp final result
    return FMath::Clamp(TotalViability, 0.0f, 1.0f);
}

FLinearColor FBiomeDataEditor::GetViabilityColor(float Viability) const
{
    // Color gradient: Black -> Red -> Yellow -> Green
    FLinearColor Color;

    if (Viability < 0.33f)
    {
        // Black to Red
        float T = Viability / 0.33f;
        Color = FLinearColor::LerpUsingHSV(FLinearColor::Black, FLinearColor::Red, T);
    }
    else if (Viability < 0.66f)
    {
        // Red to Yellow
        float T = (Viability - 0.33f) / 0.33f;
        Color = FLinearColor::LerpUsingHSV(FLinearColor::Red, FLinearColor::Yellow, T);
    }
    else
    {
        // Yellow to Green
        float T = (Viability - 0.66f) / 0.34f;
        Color = FLinearColor::LerpUsingHSV(FLinearColor::Yellow, FLinearColor::Green, T);
    }

    // Add some transparency for low viability
    Color.A = FMath::Max(0.3f, Viability);

    return Color;
}

// Details Customization Implementation
TSharedRef<IDetailCustomization> FBiomeDataDetailsCustomization::MakeInstance()
{
    return MakeShareable(new FBiomeDataDetailsCustomization());
}

void FBiomeDataDetailsCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // get the objects being customized
    TArray<TWeakObjectPtr<UObject>> Objects;
    DetailBuilder.GetObjectsBeingCustomized(Objects);
    if (Objects.Num() == 0) return;

    UBiomeData* EditedBiomeData = Cast<UBiomeData>(Objects[0].Get());
    if (!EditedBiomeData) return;

    // Now get the array handle, count elements, etc:
    TSharedPtr<IPropertyHandle> SpeciesProperty = DetailBuilder.GetProperty(GET_MEMBER_NAME_CHECKED(UBiomeData, Species));
    if (!SpeciesProperty.IsValid()) return;

    TSharedPtr<IPropertyHandleArray> SpeciesArray = SpeciesProperty->AsArray();
    if (!SpeciesArray.IsValid()) return;

    uint32 NumSpecies = 0;
    SpeciesArray->GetNumElements(NumSpecies);

    IDetailCategoryBuilder& SpeciesCategory = DetailBuilder.EditCategory("Species");

    for (uint32 Index = 0; Index < NumSpecies; ++Index)
    {
        // Read the species name
        TSharedRef<IPropertyHandle> ElemHandle = SpeciesArray->GetElement(Index);
        TSharedPtr<IPropertyHandle> NameHandle = ElemHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FBiomeSpecies, SpeciesName));

        FString SpeciesName = TEXT("?");
        if (NameHandle.IsValid())
        {
            NameHandle->GetValue(SpeciesName);
        }

        // Add a button row
        SpeciesCategory.AddCustomRow(FText::FromString("Preview"))
            .ValueContent()
            [
                SNew(SButton)
                    .Text(FText::Format(FText::FromString("Preview {0} Viability"), FText::FromString(SpeciesName)))
                    .OnClicked_Lambda([EditedBiomeData, Index]()->FReply
                         {
                            if (TSharedPtr<IToolkit> Toolkit = FToolkitManager::Get().FindEditorForAsset(EditedBiomeData))
                            {
                                if (TSharedPtr<FBiomeDataEditor> Editor = StaticCastSharedPtr<FBiomeDataEditor>(Toolkit))
                                {
                                    Editor->PreviewSpeciesViability(Index);
                                }
                            }
                     return FReply::Handled();
                    })
            ];
    }
}



// Viability Preview Widget Implementation
void SBiomeViabilityPreviewWidget::Construct(const FArguments& InArgs, FBiomeDataEditor* InEditor)
{
    BiomeEditor = InEditor;

    ChildSlot
        [
            SNew(SVerticalBox)
                + SVerticalBox::Slot()
                .FillHeight(1.0f)
                .Padding(5)
                [
                    SNew(SBox)
                        .WidthOverride(512)
                        .HeightOverride(512)
                        [
                            SAssignNew(PreviewImage, SImage)
                                .Image(FEditorStyle::GetBrush("WhiteBrush"))
                        ]
                ]
                + SVerticalBox::Slot()
                .AutoHeight()
                .Padding(5)
                [
                    SNew(SHorizontalBox)
                        + SHorizontalBox::Slot()
                        .FillWidth(1.0f)
                        .Padding(2)
                        [
                            SAssignNew(LandscapeSelector, SObjectPropertyEntryBox)
                                .AllowedClass(ALandscapeStreamingProxy::StaticClass())
                                .ObjectPath_Lambda([this]() -> FString {
                                    if (BiomeEditor != nullptr)
                                    {
                                        ALandscapeStreamingProxy* Selected = BiomeEditor->GetSelectedLandscapeProxy();
                                        return Selected ? Selected->GetPathName() : FString();
                                    }
                                    return FString();
                                })
                                .OnObjectChanged(this, &SBiomeViabilityPreviewWidget::OnLandscapeChanged)
                                .DisplayUseSelected(true)
                                .DisplayBrowse(true)
                                .DisplayThumbnail(false)
                        ]
                        + SHorizontalBox::Slot()
                        .AutoWidth()
                        .Padding(2)
                        [
                            SNew(SButton)
                                .Text(FText::FromString("Bake Terrain Data"))
                                .OnClicked(this, &SBiomeViabilityPreviewWidget::OnBakeTerrainClicked)
                        ]
                ]
                + SVerticalBox::Slot()
                .AutoHeight()
                .Padding(5)
                [
                    SAssignNew(InfoText, STextBlock)
                        .Text(FText::FromString("Select a landscape proxy and bake terrain data to preview species viability."))
                ]
        ];
}

void SBiomeViabilityPreviewWidget::SetPreviewTexture(UTexture2D* InTexture)
{
    if (InTexture && PreviewImage.IsValid())
    {
        FSlateBrush* Brush = new FSlateBrush();
        Brush->SetResourceObject(InTexture);
        Brush->ImageSize = FVector2D(512, 512);
        PreviewImage->SetImage(Brush);
    }
}

void SBiomeViabilityPreviewWidget::OnLandscapeChanged(const FAssetData& AssetData)
{
    if (BiomeEditor != nullptr)
    {
        ALandscapeStreamingProxy* NewProxy = Cast<ALandscapeStreamingProxy>(AssetData.GetAsset());
        BiomeEditor->SetSelectedLandscapeProxy(NewProxy);

        if (InfoText.IsValid())
        {
            if (NewProxy)
            {
                InfoText->SetText(FText::Format(
                    FText::FromString("Selected: {0}. Click 'Bake Terrain Data' to analyze terrain attributes."),
                    FText::FromString(NewProxy->GetName())
                ));
            }
            else
            {
                InfoText->SetText(FText::FromString("No landscape selected."));
            }
        }
    }
}

FReply SBiomeViabilityPreviewWidget::OnBakeTerrainClicked()
{
    if (BiomeEditor != nullptr)
    {
        BiomeEditor->BakeTerrainData(); // Direct call
    }
    return FReply::Handled();
}

